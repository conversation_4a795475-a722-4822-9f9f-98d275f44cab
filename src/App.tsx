import {Suspense, useEffect, useState} from 'react';
import {Modal} from 'acud';

import {Provider} from 'react-redux';
import store from '@store/index';

import {useDocumentTitle, useAppContext, AppContextActionType} from '@baidu/bce-react-toolkit';
import {Loading} from 'acud';
import Router from './router/router';
import {getGlobalPrivilege} from '@utils/auth';
import {useRegion} from '@hooks/useRegion';
import {queryIamStsRole, setGlobalRegion, checkWhiteList, queryEdapActive, getEditionInfo} from '@api/auth';
import {REGION_SETTINGS} from '@utils/utils';
import flags from './flags';
import {useCookieState} from 'ahooks';
import {playgroundInfo} from '@helpers/palyground-info';
const isPrivate = flags?.DatabuilderPrivateSwitch;
console.info('❄️ isPrivate:', isPrivate);
const isQasandbox = window.location.hostname.includes('qasandbox');
const baseUrl = isQasandbox ? 'https://qasandbox.bcetest.baidu.com' : 'https://console.bce.baidu.com';
export default function App() {
  // 权限加载状态 - 确保权限加载完成后再渲染路由
  const [initLoading, setInitLoading] = useState(true);
  const {appState, appDispatch} = useAppContext();

  const onRegionChange = () => {
    document.querySelector('#bce-content .header .content .header-select .select-items')?.remove();
    window.location.reload();
  };
  useRegion({onRegionChange});

  // 获取主账户的 accountid
  const [accountid] = useCookieState('bce-login-accountid');
  // 是否为 playground 用户
  const isPlayGroundUser = accountid === playgroundInfo.accountId;

  async function init() {
    setInitLoading(true);

    try {
      // 非私有化模式 & 非playground用户， 请求白名单和请求开通逻辑
      if (!isPrivate && !isPlayGroundUser) {
        // 成都region不进行激活和版本判断
        const currentRegion =
          window.$framework.region.getCurrentRegion()?.id || REGION_SETTINGS.DEFAULT_REGION;
        if (REGION_SETTINGS.WITH_EDAP_REGION.includes(currentRegion)) {
          // 白名单检查
          // const whiteListCheck = await checkWhiteList();
          // if (!whiteListCheck.success) {
          //   throw new Error('白名单获取失败');
          // }

          // if (!whiteListCheck.result.inWhitelist) {
          //   // 跳转到白名单申请页面
          //   window.location.href = 'https://cloud.baidu.com/survey/databuilderapply.html';
          //   return;
          // }

          // 仅公有云具备激活和购买逻辑
          // 查询是否激活产品
          const iamRoleInfo = await queryIamStsRole();
          const isEdapActive = await queryEdapActive();
          if (iamRoleInfo.success && isEdapActive.success) {
            if (iamRoleInfo.result?.id && isEdapActive.result?.isActivated) {
              appDispatch({
                type: AppContextActionType.ACTIVATE_PRODUCT
              });
            } else {
              // 未激活产品，不继续请求权限，直接进入激活页面
              setInitLoading(false);
              // 由EDAP处理激活逻辑
              window.location.href = `${baseUrl}/edap/#/active`;
              return;
            }
          } else {
            throw new Error('激活产品获取失败');
          }
          // 查询是否购买产品（EDAP接口）
          const editionRes = await getEditionInfo();
          if (editionRes.success) {
            if (editionRes.result.name) {
              store.dispatch({
                type: 'globalAuth/updateEditionInfo',
                payload: editionRes.result
              });
              window._edapShow = true;
            } else {
              // 跳转到billing购买页面
              window.location.href = `${baseUrl}/edap/#/billing?type=OPEN`;
              return;
            }
          } else {
            throw new Error('产品版本信息获取失败');
          }
        }
        // 融合态 - 成都region检查白名单，仅对白名单内用户开放
        if (currentRegion === 'cd') {
          // 白名单检查
          const whiteListCheck = await checkWhiteList();
          if (!whiteListCheck.success) {
            throw new Error('白名单获取失败');
          }

          if (!whiteListCheck.result.inWhitelist) {
            // 控制仅能跳转到遮挡页面
            store.dispatch({
              type: 'globalAuth/updateAvailableInChengdu',
              payload: false
            });
          }
        }
      }

      setInitLoading(false);
    } catch (error) {
      console.error(error);
      Modal.confirm({
        title: '提示',
        content: '初始化失败，请点击重试!',
        onOk: () => {
          init();
        }
      });
      setInitLoading(false);
    }
  }

  // 获取全局权限
  useEffect(() => {
    init();
    // 非私有化模式 处理region
    if (!flags.DatabuilderPrivateSwitch) {
      const currentRegion = window.$framework.region.getCurrentRegion()?.id || REGION_SETTINGS.DEFAULT_REGION;
      // playground 用户强制切换到默认 bd region
      if (isPlayGroundUser && currentRegion !== REGION_SETTINGS.DEFAULT_REGION) {
        setGlobalRegion({regionId: REGION_SETTINGS.DEFAULT_REGION}).then(() => {
          onRegionChange();
        });
      } else if (
        !REGION_SETTINGS.SUPPORT_REGION.includes(currentRegion) &&
        !window.location.host.includes('localhost')
      ) {
        setGlobalRegion({regionId: REGION_SETTINGS.DEFAULT_REGION}).then(() => {
          onRegionChange();
        });
      }
      // 处理region disable
      document
        .querySelectorAll('#bce-content .header .content .header-select .select-items li > .select-item')
        ?.forEach((item) => {
          const curItem = item as HTMLElement;
          if (
            curItem &&
            curItem.style &&
            !REGION_SETTINGS.SUPPORT_REGION.includes(curItem?.getAttribute('data-region') || '')
          ) {
            const parent = curItem.parentNode;
            const grandParent = parent?.parentNode;
            grandParent?.removeChild(parent);
          }
        });
    }
  }, []);

  useEffect(() => {
    getGlobalPrivilege().then((res) => {
      store.dispatch({
        type: 'globalAuth/updateGlobalPermission',
        payload: res
      });
    });
  }, []);

  return (
    <Provider store={store}>
      <Suspense fallback={<Loading loading />}>{initLoading ? <Loading loading /> : <Router />}</Suspense>
    </Provider>
  );
}
