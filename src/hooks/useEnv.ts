import flags from '@/flags';
import Cookies from 'js-cookie';
import {playgroundInfo} from '@/helpers/palyground-info';
const DatabuilderPrivateSwitch = flags.DatabuilderPrivateSwitch;
const accountid = Cookies.get('bce-login-accountid');

export function getEnv() {
  return {
    isPrivate: DatabuilderPrivateSwitch,
    isQasandbox: window.location.hostname.includes('qasandbox'),
    isPlayGroundUser: accountid === playgroundInfo.accountId
  };
}

export default function useEnv() {
  return getEnv();
}
